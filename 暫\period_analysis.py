import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

def analyze_periods():
    # 讀取CSV檔案
    df = pd.read_csv('data/lotto_history_2007_2025.csv')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 期數統計分析 ===')
    print(f'總期數: {len(df)}')
    print()
    
    # 轉換期號為整數
    df['period'] = df['period'].astype(int)
    
    # 按年份分組分析
    df['year'] = df['date'].str.split('/').str[0].astype(int)
    
    print('=== 各年度期數統計 ===')
    year_stats = df.groupby('year').agg({
        'period': ['count', 'min', 'max'],
        'date': ['min', 'max']
    }).round(0)
    
    year_stats.columns = ['期數', '最小期號', '最大期號', '開始日期', '結束日期']
    print(year_stats)
    print()
    
    # 檢查期號連續性
    print('=== 期號連續性分析 ===')
    periods = df['period'].values
    gaps = []
    
    for i in range(1, len(periods)):
        expected_next = periods[i-1] + 1
        actual_next = periods[i]
        if actual_next != expected_next:
            gap_size = actual_next - expected_next
            gaps.append({
                '位置': i+1,
                '前一期號': periods[i-1],
                '預期下一期': expected_next,
                '實際下一期': actual_next,
                '跳躍大小': gap_size,
                '前一期日期': df.iloc[i-1]['date'],
                '後一期日期': df.iloc[i]['date'],
                '前一年份': df.iloc[i-1]['year'],
                '後一年份': df.iloc[i]['year']
            })
    
    if gaps:
        print(f'發現 {len(gaps)} 個期號跳躍:')
        print()
        for gap in gaps:
            print(f'第 {gap["位置"]} 行:')
            print(f'  前一期: {gap["前一期號"]} ({gap["前一期日期"]}, {gap["前一年份"]}年)')
            print(f'  預期下一期: {gap["預期下一期"]}')
            print(f'  實際下一期: {gap["實際下一期"]} ({gap["後一期日期"]}, {gap["後一年份"]}年)')
            print(f'  跳躍大小: {gap["跳躍大小"]}')
            print()
    else:
        print('所有期號都連續')
    
    # 分析期號模式
    print('=== 期號模式分析 ===')
    for year in sorted(df['year'].unique()):
        year_data = df[df['year'] == year]
        year_periods = year_data['period'].values
        
        print(f'\n{year}年:')
        print(f'  期數範圍: {year_periods.min()} - {year_periods.max()}')
        print(f'  總期數: {len(year_periods)}')
        
        # 檢查該年度內是否連續
        year_gaps = []
        for i in range(1, len(year_periods)):
            if year_periods[i] != year_periods[i-1] + 1:
                year_gaps.append((year_periods[i-1], year_periods[i]))
        
        if year_gaps:
            print(f'  年度內跳躍: {len(year_gaps)} 個')
            for prev, curr in year_gaps:
                print(f'    {prev} -> {curr} (跳躍 {curr - prev - 1})')
        else:
            print('  年度內連續')
    
    # 統計分析
    print('\n=== 統計摘要 ===')
    print(f'期號範圍: {periods.min()} - {periods.max()}')
    print(f'期號總跨度: {periods.max() - periods.min() + 1}')
    print(f'實際期數: {len(periods)}')
    print(f'缺失期數: {periods.max() - periods.min() + 1 - len(periods)}')
    print(f'跳躍次數: {len(gaps)}')
    
    # 計算跳躍統計
    if gaps:
        jump_sizes = [gap['跳躍大小'] for gap in gaps]
        print(f'平均跳躍大小: {np.mean(jump_sizes):.1f}')
        print(f'最大跳躍大小: {max(jump_sizes)}')
        print(f'最小跳躍大小: {min(jump_sizes)}')
    
    # 按年份統計期數
    print('\n=== 年度期數統計 ===')
    yearly_counts = df.groupby('year')['period'].count()
    print(yearly_counts)
    
    # 檢查是否有異常的年度期數
    print('\n=== 年度期數異常檢查 ===')
    avg_yearly_count = yearly_counts.mean()
    print(f'平均年度期數: {avg_yearly_count:.1f}')
    
    for year, count in yearly_counts.items():
        if abs(count - avg_yearly_count) > avg_yearly_count * 0.2:  # 超過平均值的20%
            print(f'{year}年: {count}期 (異常: 偏離平均值 {count - avg_yearly_count:.1f})')
    
    # 詳細的期號列表（前20期和後20期）
    print('\n=== 期號詳細列表 ===')
    print('前20期:')
    for i in range(min(20, len(df))):
        row = df.iloc[i]
        print(f'  {i+1:3d}. 期號: {row["period"]:9d}, 日期: {row["date"]}, 年份: {row["year"]}')
    
    print('\n後20期:')
    for i in range(max(0, len(df)-20), len(df)):
        row = df.iloc[i]
        print(f'  {i+1:3d}. 期號: {row["period"]:9d}, 日期: {row["date"]}, 年份: {row["year"]}')
    
    return df, gaps

def visualize_period_analysis(df, gaps):
    """視覺化期數分析"""
    try:
        font = FontProperties(fname=r"c:\windows\fonts\msjh.ttc", size=12)
    except FileNotFoundError:
        try:
            font = FontProperties(fname="/System/Library/Fonts/PingFang.ttc", size=12)
        except FileNotFoundError:
            font = FontProperties(size=12)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('大樂透期數分析', fontsize=16, fontproperties=font)
    
    # 1. 期號時間序列
    ax1.plot(range(len(df)), df['period'], 'b-', alpha=0.7)
    ax1.set_title('期號時間序列', fontproperties=font)
    ax1.set_xlabel('期數順序', fontproperties=font)
    ax1.set_ylabel('期號', fontproperties=font)
    ax1.grid(True, alpha=0.3)
    
    # 標記跳躍點
    if gaps:
        jump_positions = [gap['位置']-1 for gap in gaps]
        jump_periods = [gap['實際下一期'] for gap in gaps]
        ax1.scatter(jump_positions, jump_periods, color='red', s=50, zorder=5, label='跳躍點')
        ax1.legend(prop=font)
    
    # 2. 年度期數分布
    yearly_counts = df.groupby('year')['period'].count()
    ax2.bar(yearly_counts.index, yearly_counts.values, alpha=0.7)
    ax2.set_title('各年度期數分布', fontproperties=font)
    ax2.set_xlabel('年份', fontproperties=font)
    ax2.set_ylabel('期數', fontproperties=font)
    ax2.grid(True, alpha=0.3)
    
    # 3. 期號範圍分布
    ax3.hist(df['period'], bins=50, alpha=0.7, edgecolor='black')
    ax3.set_title('期號分布直方圖', fontproperties=font)
    ax3.set_xlabel('期號', fontproperties=font)
    ax3.set_ylabel('頻率', fontproperties=font)
    ax3.grid(True, alpha=0.3)
    
    # 4. 跳躍大小分析
    if gaps:
        jump_sizes = [gap['跳躍大小'] for gap in gaps]
        ax4.bar(range(len(jump_sizes)), jump_sizes, alpha=0.7)
        ax4.set_title('跳躍大小分析', fontproperties=font)
        ax4.set_xlabel('跳躍序號', fontproperties=font)
        ax4.set_ylabel('跳躍大小', fontproperties=font)
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, '無跳躍', ha='center', va='center', transform=ax4.transAxes, fontproperties=font)
        ax4.set_title('跳躍大小分析', fontproperties=font)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    df, gaps = analyze_periods()
    visualize_period_analysis(df, gaps) 