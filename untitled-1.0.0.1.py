import pandas as pd
import numpy as np

def analyze_number_streaks():
    """
    分析每个号码未开出的期数，追踪每个号码自上次开出以来经过了多少期
    """
    # 读取CSV文件
    df = pd.read_csv('data/2015-2025.csv')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 号码未开出期数统计分析 ===')
    
    # 转换期号为整数
    df['period'] = df['period'].astype(int)
    df = df.sort_values('period')  # 确保按期号排序
    
    # 创建号码列表
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 初始化追踪字典，记录每个号码当前的未出现期数
    number_streaks = {}    # 记录每个号码当前的未出现期数
    
    # 初始化所有号码
    for i in range(1, 50):
        number_streaks[i] = 0
    
    # 按期分析
    streak_analysis = []

    for i, row in df.iterrows():
        current_period = row['period']

        # 获取当期开出的号码
        current_numbers = [row[col] for col in number_columns]

        # 记录本期各号码的未出现期数（在更新之前记录）
        streak_record = {
            '期号': current_period,
            '日期': row['date'],
        }

        # 添加每个号码的未出现期数（记录开奖前的状态）
        for num in range(1, 50):
            streak_record[f'号码{num}'] = number_streaks[num]

        # 更新号码状态（在记录之后更新）
        for num in range(1, 50):
            # 如果该号码在这期开出，则重置计数器
            if num in current_numbers:
                number_streaks[num] = 0
            else:
                # 否则增加未出现期数
                number_streaks[num] += 1
            
        # 统计本期信息
        streak_record['最大未出现期数'] = max(number_streaks.values())
        streak_record['平均未出现期数'] = np.mean(list(number_streaks.values()))
        streak_record['开出号码'] = current_numbers
        
        streak_analysis.append(streak_record)
    
    streak_df = pd.DataFrame(streak_analysis)
    
    # 显示最近几期的分析结果
    print('\n最近50期各号码未出现期数:')
    recent_periods = streak_df.tail(50)
    for _, row in recent_periods.iterrows():
        print(f"期号: {row['期号']} ({row['日期']})")
        # 获取每个开奖号码的未出现期数
        current_numbers = row['开出号码']
        streaks_for_numbers = [str(row[f'号码{num}']) + "期" for num in current_numbers]
        print(f"  开出号码: {row['开出号码']} " + ",".join(streaks_for_numbers))
        print(f"  最大未出现期数: {row['最大未出现期数']}")
        print("  未出现期数前49个的号码: ")

        # 获取未出现期数最多的49个号码
        numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        numbers_streaks.sort(key=lambda x: x[1], reverse=True)
        top_49_streaks = numbers_streaks[:49]

        # 按未出现期数分组显示
        streaks_by_period = {}
        for num, streak in top_49_streaks:
            if streak not in streaks_by_period:
                streaks_by_period[streak] = []
            streaks_by_period[streak].append(str(num))

        # 按期数降序打印
        sorted_streaks_keys = sorted(streaks_by_period.keys(), reverse=True)
        
        for streak in sorted_streaks_keys:
            numbers = streaks_by_period[streak]
            numbers.sort(key=int)  # 对号码进行排序
            numbers_str = ", ".join(numbers)
            print(f"        [{streak}期] {numbers_str}")
        print()
    
    # 统计各号码的历史最大未出现期数
    print('=== 各号码历史最大未出现期数 ===')
    max_streaks = {}
    for num in range(1, 50):
        max_streaks[num] = streak_df[f'号码{num}'].max()
    
    # 按最大未出现期数排序
    sorted_streaks = sorted(max_streaks.items(), key=lambda x: x[1], reverse=True)
    
    print('历史最大未出现期数排名前15的号码:')
    for i, (num, streak) in enumerate(sorted_streaks[:15]):
        print(f"  {i+1:2d}. 号码{num:2d}: {streak}期")
    
    # 分析特定号码的未出现期数分布
    print('\n=== 当前各号码未出现期数 ===')
    current_streaks = streak_df.iloc[-1]
    current_numbers = [(num, current_streaks[f'号码{num}']) for num in range(1, 50)]
    current_numbers.sort(key=lambda x: x[1], reverse=True)
    
    print('当前未出现期数排名前15的号码:')
    for i, (num, streak) in enumerate(current_numbers[:15]):
        print(f"  {i+1:2d}. 号码{num:2d}: {streak}期")
    
    # 分析未出现期数的分布情况
    print('\n=== 未出现期数分布情况 ===')
    latest_streaks = [current_streaks[f'号码{num}'] for num in range(1, 50)]
    print(f"平均未出现期数: {np.mean(latest_streaks):.2f}")
    print(f"中位数未出现期数: {np.median(latest_streaks):.2f}")
    print(f"最大未出现期数: {np.max(latest_streaks)}")
    print(f"最小未出现期数: {np.min(latest_streaks)}")
    
    # 统计不同未出现期数范围的号码数量
    ranges = [(0, 5), (6, 10), (11, 15), (16, 20), (21, 25), (26, 30), (31, 100)]
    print('\n不同未出现期数范围的号码数量:')
    for start, end in ranges:
        count = len([s for s in latest_streaks if start <= s <= end])
        print(f"  {start:2d}-{end:2d}期: {count:2d}个号码")
    
    # 导出详细数据到CSV
    export_columns = ['期号', '日期', '开出号码', '最大未出现期数', '平均未出现期数']
    for num in range(1, 50):
        export_columns.append(f'号码{num}')
    
    export_df = streak_df[export_columns].copy()
    export_df['开出号码'] = export_df['开出号码'].apply(lambda x: ','.join(map(str, x)))
    
    export_df.to_csv('number_streaks_analysis.csv', index=False, encoding='utf-8-sig')
    print('\n详细分析结果已导出至 number_streaks_analysis.csv')
    
    return streak_df

def analyze_cold_hot_numbers(streak_df, threshold=20):
    """
    分析冷热号码（未出现期数超过阈值的为冷号，低于阈值的为热号）
    """
    latest_streaks = streak_df.iloc[-1]
    
    cold_numbers = []  # 冷号（长期未出现）
    hot_numbers = []   # 热号（近期常出现）
    
    for num in range(1, 50):
        streak = latest_streaks[f'号码{num}']
        if streak >= threshold:
            cold_numbers.append((num, streak))
        else:
            hot_numbers.append((num, streak))
    
    print(f'\n=== 冷热号码分析 (阈值: {threshold}期) ===')
    print(f'冷号数量: {len(cold_numbers)}')
    print(f'热号数量: {len(hot_numbers)}')
    
    print('\n冷号列表 (未出现期数 >= {}期):'.format(threshold))
    cold_numbers.sort(key=lambda x: x[1], reverse=True)
    for num, streak in cold_numbers:
        print(f"  号码{num:2d}: {streak}期")
    
    print('\n热号列表 (未出现期数 < {}期):'.format(threshold))
    hot_numbers.sort(key=lambda x: x[1])
    for num, streak in hot_numbers[:10]:  # 只显示前10个最热的号码
        print(f"  号码{num:2d}: {streak}期")

if __name__ == "__main__":
    streak_df = analyze_number_streaks()
    analyze_cold_hot_numbers(streak_df, threshold=20)
