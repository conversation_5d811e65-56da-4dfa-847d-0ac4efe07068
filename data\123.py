import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

# --- 步驟 1: 建立模擬股價資料 ---
# 為了讓範例可重現，我們自己生成一些資料
# 實際應用中，您會從 CSV 檔案或財經 API (如 yfinance) 讀取資料
np.random.seed(42)
days = 300
close_prices = 100 + np.random.randn(days).cumsum() + np.sin(np.linspace(0, 20, days)) * 5
dates = pd.to_datetime(pd.date_range('2024-01-01', periods=days))

df = pd.DataFrame({'Close': close_prices}, index=dates)

# --- 步驟 2: 定義計算雙線乖離的函式 ---
def calculate_dual_bias(data, short_window, long_window):
    """
    計算並在 DataFrame 中加入短、長天期的移動平均線和 BIAS 值。

    Args:
        data (pd.DataFrame): 包含 'Close' 欄位的 DataFrame。
        short_window (int): 短天期移動平均線的週期。
        long_window (int): 長天期移動平均線的週期。

    Returns:
        pd.DataFrame: 包含計算結果的新 DataFrame。
    """
    df_copy = data.copy()
    
    # 計算移動平均線 (MA)
    df_copy[f'MA_{short_window}'] = df_copy['Close'].rolling(window=short_window).mean()
    df_copy[f'MA_{long_window}'] = df_copy['Close'].rolling(window=long_window).mean()
    
    # 計算 BIAS (乖離率)
    # 公式: (收盤價 - N日均價) / N日均價 * 100
    df_copy[f'BIAS_{short_window}'] = (df_copy['Close'] - df_copy[f'MA_{short_window}']) / df_copy[f'MA_{short_window}'] * 100
    df_copy[f'BIAS_{long_window}'] = (df_copy['Close'] - df_copy[f'MA_{long_window}']) / df_copy[f'MA_{long_window}'] * 100
    
    return df_copy

# --- 步驟 3: 應用函式並計算指標 ---
# 我們選用 6 日和 12 日作為短、長天期
short_period = 20
long_period = 60
df_with_bias = calculate_dual_bias(df, short_window=short_period, long_window=long_period)

# 顯示計算結果的末幾筆資料
print("計算結果預覽：")
print(df_with_bias.tail())

# --- 步驟 4: 數據可視化 (繪圖) ---
# 設置中文字體，請根據您的作業系統選擇存在的字體
# Windows: 'Microsoft JhengHei' or 'SimSun'
# macOS: 'Arial Unicode MS' or 'PingFang TC'
# Linux: 'WenQuanYi Micro Hei'
try:
    font = FontProperties(fname=r"c:\windows\fonts\msjh.ttc", size=12) # Windows
except FileNotFoundError:
    try:
        font = FontProperties(fname="/System/Library/Fonts/PingFang.ttc", size=12) # macOS
    except FileNotFoundError:
        font = FontProperties(size=12) # 預設字體

# 建立 2x1 的子圖，共享 x 軸
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), sharex=True)
fig.suptitle('雙線乖離 (Dual BIAS Lines) 分析', fontsize=20, fontproperties=font)

# 上圖: 股價與移動平均線
ax1.plot(df_with_bias.index, df_with_bias['Close'], label='收盤價 (Close)', color='black', alpha=0.8)
ax1.plot(df_with_bias.index, df_with_bias[f'MA_{short_period}'], label=f'{short_period}日均線 (MA)', linestyle='--', color='blue')
ax1.plot(df_with_bias.index, df_with_bias[f'MA_{long_period}'], label=f'{long_period}日均線 (MA)', linestyle='--', color='red')
ax1.set_title('股價與移動平均線', fontproperties=font)
ax1.set_ylabel('價格', fontproperties=font)
ax1.legend(prop=font)
ax1.grid(True)

# 下圖: 雙線乖離指標
ax2.plot(df_with_bias.index, df_with_bias[f'BIAS_{short_period}'], label=f'{short_period}日 BIAS (短)', color='blue')
ax2.plot(df_with_bias.index, df_with_bias[f'BIAS_{long_period}'], label=f'{long_period}日 BIAS (長)', color='red')
ax2.axhline(0, color='gray', linestyle='--') # 加上 0 軸參考線
ax2.set_title('雙線乖離指標', fontproperties=font)
ax2.set_ylabel('乖離率 (%)', fontproperties=font)
ax2.set_xlabel('日期', fontproperties=font)
ax2.legend(prop=font)
ax2.grid(True)

# 自動調整佈局並顯示圖表
plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.show()