import pandas as pd
import re

def check_csv_file():
    # 讀取CSV檔案
    df = pd.read_csv('data/lotto_history_2007_2025.csv')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 檔案基本資訊 ===')
    print(f'總行數: {len(df)}')
    print(f'欄位數: {len(df.columns)}')
    print()
    
    print('=== 檢查日期格式 ===')
    # 檢查日期格式是否正確
    date_pattern = r'^\d{4}/\d{1,2}/\d{1,2}$'
    invalid_dates = []
    for i, date in enumerate(df['date']):
        if not re.match(date_pattern, str(date)):
            invalid_dates.append((i+1, date))
    
    if invalid_dates:
        print(f'發現 {len(invalid_dates)} 個無效日期格式:')
        for line, date in invalid_dates[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {date}')
    else:
        print('所有日期格式都正確')
    print()
    
    print('=== 檢查期號編號 ===')
    # 檢查期號是否連續
    periods = df['period'].astype(int)
    period_errors = []
    for i in range(1, len(periods)):
        if periods.iloc[i] != periods.iloc[i-1] + 1:
            period_errors.append((i+1, periods.iloc[i-1], periods.iloc[i]))
    
    if period_errors:
        print(f'發現 {len(period_errors)} 個期號不連續:')
        for line, prev, curr in period_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {prev} -> {curr}')
    else:
        print('期號編號連續')
    print()
    
    print('=== 檢查號碼範圍 ===')
    # 檢查號碼是否在1-49範圍內
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    number_errors = []
    for i, row in df.iterrows():
        for col in number_columns:
            num = int(row[col])
            if num < 1 or num > 49:
                number_errors.append((i+1, col, num))
    
    if number_errors:
        print(f'發現 {len(number_errors)} 個號碼超出範圍(1-49):')
        for line, col, num in number_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行 {col}: {num}')
    else:
        print('所有號碼都在1-49範圍內')
    print()
    
    print('=== 檢查號碼重複 ===')
    # 檢查同一行是否有重複號碼
    duplicate_errors = []
    for i, row in df.iterrows():
        numbers = [int(row[col]) for col in number_columns]
        if len(set(numbers)) != 6:
            duplicate_errors.append((i+1, numbers))
    
    if duplicate_errors:
        print(f'發現 {len(duplicate_errors)} 行有重複號碼:')
        for line, numbers in duplicate_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {numbers}')
    else:
        print('沒有發現重複號碼')
    print()
    
    print('=== 檢查年份範圍 ===')
    # 檢查年份是否在2015-2025範圍內
    years = df['date'].str.split('/').str[0].astype(int)
    year_errors = []
    for i, year in enumerate(years):
        if year < 2007 or year > 2025:
            year_errors.append((i+1, year))
    
    if year_errors:
        print(f'發現 {len(year_errors)} 個年份超出範圍(2015-2025):')
        for line, year in year_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {year}')
    else:
        print('所有年份都在2007-2025範圍內')
    print()
    
    print('=== 檢查檔案名稱一致性 ===')
    # 檢查檔案名稱是否與年份一致
    filename_errors = []
    for i, row in df.iterrows():
        filename_year = row['filename'].split('_')[1].split('.')[0]
        date_year = str(row['date']).split('/')[0]
        if filename_year != date_year:
            filename_errors.append((i+1, row['filename'], date_year))
    
    if filename_errors:
        print(f'發現 {len(filename_errors)} 個檔案名稱與年份不一致:')
        for line, filename, year in filename_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {filename} vs {year}')
    else:
        print('檔案名稱與年份一致')
    print()
    
    print('=== 檢查號碼排序 ===')
    # 檢查號碼是否按順序排列
    unsorted_errors = []
    for i, row in df.iterrows():
        numbers = [int(row[col]) for col in number_columns]
        if numbers != sorted(numbers):
            unsorted_errors.append((i+1, numbers))
    
    if unsorted_errors:
        print(f'發現 {len(unsorted_errors)} 行號碼未按順序排列:')
        for line, numbers in unsorted_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {numbers}')
    else:
        print('所有號碼都按順序排列')
    print()
    
    print('=== 檢查遊戲類型 ===')
    # 檢查遊戲類型是否都是"大樂透"
    game_type_errors = []
    for i, game_type in enumerate(df['game_type']):
        if game_type != '大樂透':
            game_type_errors.append((i+1, game_type))
    
    if game_type_errors:
        print(f'發現 {len(game_type_errors)} 個遊戲類型不是"大樂透":')
        for line, game_type in game_type_errors[:10]:  # 只顯示前10個
            print(f'  第 {line} 行: {game_type}')
    else:
        print('所有遊戲類型都是"大樂透"')
    print()
    
    # 總結
    total_errors = len(invalid_dates) + len(period_errors) + len(number_errors) + len(duplicate_errors) + len(year_errors) + len(filename_errors) + len(unsorted_errors) + len(game_type_errors)
    
    print('=== 檢查總結 ===')
    if total_errors == 0:
        print('檔案檢查完成，沒有發現錯誤！')
    else:
        print(f'檔案檢查完成，發現 {total_errors} 個錯誤：')
        print(f'  - 日期格式錯誤: {len(invalid_dates)} 個')
        print(f'  - 期號不連續: {len(period_errors)} 個')
        print(f'  - 號碼超出範圍: {len(number_errors)} 個')
        print(f'  - 重複號碼: {len(duplicate_errors)} 個')
        print(f'  - 年份超出範圍: {len(year_errors)} 個')
        print(f'  - 檔案名稱不一致: {len(filename_errors)} 個')
        print(f'  - 號碼未排序: {len(unsorted_errors)} 個')
        print(f'  - 遊戲類型錯誤: {len(game_type_errors)} 個')

if __name__ == "__main__":
    check_csv_file() 