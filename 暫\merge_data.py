import pandas as pd
import os

def merge_and_clean_data():
    """
    合併2007-2025和2015-2025的CSV檔案，並清理數據。
    """
    # 定義檔案路徑
    file_2007_2025 = 'data/2007-2025.csv'
    file_2015_2025 = 'data/2015-2025.csv'
    output_file = 'data/lotto_history_2007_2025.csv'
    
    # 檢查檔案是否存在
    if not os.path.exists(file_2007_2025):
        print(f"錯誤: 找不到檔案 {file_2007_2025}")
        return
    if not os.path.exists(file_2015_2025):
        print(f"錯誤: 找不到檔案 {file_2015_2025}")
        return

    # 讀取CSV檔案
    df_2007 = pd.read_csv(file_2007_2025)
    df_2015 = pd.read_csv(file_2015_2025, header=None)
    
    # 重新命名 df_2015 的欄位以匹配 df_2007
    df_2015.columns = df_2007.columns

    # 合併兩個DataFrame
    combined_df = pd.concat([df_2007, df_2015], ignore_index=True)
    
    # 移除基於 '期別' 的重複項，保留第一個出現的
    cleaned_df = combined_df.drop_duplicates(subset='期別', keep='first')
    
    # 按 '期別' 排序
    cleaned_df = cleaned_df.sort_values(by='期別').reset_index(drop=True)
    
    # 儲存清理後的數據
    cleaned_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"合併和清理完成！")
    print(f"總筆數: {len(cleaned_df)}")
    print(f"已儲存至: {output_file}")

if __name__ == "__main__":
    merge_and_clean_data()